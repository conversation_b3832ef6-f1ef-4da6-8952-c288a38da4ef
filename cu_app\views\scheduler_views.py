from __future__ import print_function
from django.utils.dateparse import parse_datetime
from django.http import JsonResponse
from rest_framework.exceptions import APIException, ValidationError
from rest_framework import generics, status
from decimal import Decimal
from rest_framework import generics, views
from django.http import JsonResponse, HttpResponse
from cu_app.utils.helper_functions import *
from cu_app.services.airwallex import AirwallexService
from cu_admin.user_notifications import *
from ..serializers import *
from datetime import *
from rest_framework.response import Response
from rest_framework.permissions import BasePermission
from rest_framework.generics import RetrieveUpdateDestroyAPIView
import json
from django.views import View
from django.contrib.auth import get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.paginator import Paginator
# calendar
import datetime
import os.path
from calendar import monthrange
from dateutil.relativedelta import relativedelta
import zoneinfo

import re
# from datetime import datetime,date, timedelta,timezone
from django.conf import settings
from ..forms import *
from ..cu_library import *
# aws sns
import logging
import time
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from rest_framework.parsers import JSONParser
import json
# from pytz import timezone

from rest_framework.exceptions import APIException
import random
from django.db.models import Q
from sendgrid.helpers.mail import *
from python_http_client.exceptions import HTTPError
from firebase_admin.messaging import Message, Notification, WebpushConfig, WebpushFCMOptions
from fcm_django.models import FCMDevice
from django.utils import timezone
from django.utils.dateparse import parse_datetime, parse_date
import stripe
from django.shortcuts import redirect
from urllib.parse import quote
from django.core.paginator import Paginator, EmptyPage
from apscheduler.schedulers.background import BackgroundScheduler
# Initialize the background scheduler
scheduler = BackgroundScheduler()
scheduler.start()

ENV_ROOT = os.path.join(settings.BASE_DIR, '.env')
load_dotenv(ENV_ROOT)

logger = logging.getLogger(__name__)

# added


def notification_check(n_name):
    noti_check = NotificationType.objects.filter(
        NotificationName=n_name, CategoryType="N")
    if noti_check.exists():
        if NotificationType.objects.filter(NotificationName=n_name, CategoryType="N")[0].ActiveStatus == 1:
            return True
        return False
    else:
        noti_res = NotificationType.objects.create(
            NotificationName=n_name, CategoryType="N")
        return True


def email_check(e_name):
    email_check = NotificationType.objects.filter(
        NotificationName=e_name, CategoryType="E")
    if email_check.exists():
        if NotificationType.objects.filter(NotificationName=e_name, CategoryType="E")[0].ActiveStatus == 1:
            return True
        return False
    else:
        email_res = NotificationType.objects.create(
            NotificationName=e_name, CategoryType="E")
        return True

# added


# def get_cu_user_type(a):
#     try:
#         print(f"in user type---{a}")
#         b = get_user_model().objects.get(id=a)
#         u_groups=b.groups.all()
#         print(f"user groups----{u_groups}")
#         if len(u_groups)>=1:

#             return u_groups[0].name
#         else:
#             return "no valid user found"
#     except Exception as e:
#         print(f"get user exception----{e}")
#         return str(e)


def serialize_model(a, serializer):

    result = {k: v for k, v in serializer(a).data.items()}
    print(f"in filter model data   {result}")
    return result


def get_expertise_data(expertise):
    p_data1 = []
    for x in expertise:
        p_data1.append(
            serialize_model(ExpertiseCancertype.objects.filter(id__exact=x.id)[0], ExpertiseCancertypeSerializer))

    return p_data1


def filter_user_data(a):
    keys = ["is_admin", "is_active", "is_superuser", "password",
            "user_permissions", "groups", "PWVerifyCode", "PWCodeGentime"]
    result = {k: v for k, v in CuUserRegisterSerializer(
        a).data.items() if k not in keys}
    print(f"in filter user data   {a.groups.all()}")
    result['role'] = a.groups.all()[0].name
    if result['role'] == "doctor" or result['role'] == "researcher" or result['role'] == "influencer":
        print(
            f"expertise dataaaaa-----{a.expertise.all()}---{type(a.expertise.all())}")
        result['expertise'] = get_expertise_data(a.expertise.all())
        result['doctor_other_details'] = serialize_model(
            a.doctordetails, DoctorDetailsSerializer)
    elif result['role'] == "patient":
        result['patient_other_details'] = serialize_model(
            a.patientdetails, PatientDetailsSerializer)
    else:
        pass
    return result


def get_cu_user(a):
    return get_user_model().objects.get(id=a)


def check_if_slot_time_valid(a, b):

    converted_start_time = parse_datetime(a)
    converted_end_time = parse_datetime(b)
    t1 = timezone.make_aware(converted_start_time,
                             timezone.get_current_timezone())
    t2 = timezone.make_aware(
        converted_end_time, timezone.get_current_timezone())
    c = t2 - t1
    print('Difference: ', c)
    minutes = c.total_seconds() / 60
    print('Total difference in minutes: ', int(minutes))
    return int(minutes)


def check_if_slot_alreadybooked(a):

    converted_start_time = parse_datetime((a['schedule_start_time']))
    converted_end_time = parse_datetime((a['schedule_end_time']))
    t1 = timezone.make_aware(converted_start_time,
                             timezone.get_current_timezone())
    t2 = timezone.make_aware(
        converted_end_time, timezone.get_current_timezone())

    d = get_user_model().objects.get(email=a['doctor'])
    data = SchedulerSlots.objects.filter(
        schedule_start_time__exact=t1, schedule_end_time__exact=t2, doctor__exact=d)
    # data = SchedulerSlots.objects.filter((Q(doctor__exact=d,schedule_start_time__lte=t1,schedule_end_time__gte=t1)) | (Q(doctor__exact=d,schedule_start_time__lte=t2,schedule_end_time__gte=t2)))
    print(f"jwagdiq--{data}----{len(data)}")
    return data


def check_if_slot_alreadybooked_rec(a, b, c):

    converted_start_time = parse_datetime(a)
    converted_end_time = parse_datetime(b)
    t1 = timezone.make_aware(converted_start_time,
                             timezone.get_current_timezone())
    t2 = timezone.make_aware(
        converted_end_time, timezone.get_current_timezone())

    d = get_user_model().objects.get(email=c)
    # data = SchedulerSlots.objects.filter(schedule_start_time__exact=t1,schedule_end_time__exact=t2, doctor__exact=d)
    data = SchedulerSlots.objects.filter((Q(doctor__exact=d, schedule_start_time__lte=t1, schedule_end_time__gte=t1)) | (
        Q(doctor__exact=d, schedule_start_time__lte=t2, schedule_end_time__gte=t2)))

    return data


class CreateSlot(generics.CreateAPIView):
    permission_classes = []
    queryset = SchedulerSlots.objects.all()
    serializer_class = SlotSerializer

    def create(self, request, *args, **kwargs):
        print(f"timesss----{request.data}")

        # time1 = parse_datetime(request.data['schedule_start_time'])
        # time2 = parse_datetime(request.data['schedule_end_time'])
        start_date = parse_datetime(request.data['start_date'])
        time1 = parse_datetime(str(start_date.date()) +
                               "T" + request.data['schedule_start_time'])
        time2 = parse_datetime(str(start_date.date()) +
                               "T" + request.data['schedule_end_time'])
        converted_time1 = timezone.make_aware(
            time1, timezone.get_current_timezone())
        converted_time2 = timezone.make_aware(
            time2, timezone.get_current_timezone())
        print(f"time-----{timezone.get_current_timezone()}------------{timezone.localtime(converted_time1, timezone.get_current_timezone())}--------------{converted_time1}---{type(converted_time1)}---{converted_time2}---{type(converted_time2)}")
        request.data['doctor'] = get_user_model().objects.filter(
            email=request.data['doctor']).first().id
        request.data['schedule_start_time'] = converted_time1
        request.data['schedule_end_time'] = converted_time2
        return super().create(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        if request.data['recurrence'] == 1:
            end_date = parse_date(request.data['end_date'])
            start_date = parse_datetime(request.data['start_date'])
            date_arr = []
            
            start_date_raw = str(start_date.date()) + "T" + request.data['schedule_start_time']
            start_date_aware = timezone.make_aware(parse_datetime(start_date_raw), timezone.get_current_timezone())
            end_date_raw = str(start_date.date()) + "T" + request.data['schedule_end_time']
            end_date_aware = timezone.make_aware(parse_datetime(end_date_raw), timezone.get_current_timezone())
            
            while start_date_aware.date() <= end_date:
                date_arr.append([start_date_aware, end_date_aware])
                start_date_aware += timedelta(days=1)
                end_date_aware += timedelta(days=1)
            
            created_count = 0
            skipped_count = 0
            
            for slot_start, slot_end in date_arr:
                slot_start_str = slot_start.strftime('%Y-%m-%d %H:%M')
                slot_end_str = slot_end.strftime('%Y-%m-%d %H:%M')
                
                duration = check_if_slot_time_valid(slot_start_str, slot_end_str)
                if duration not in [30, 60, 90, 120, 150, 180]:
                    skipped_count += 1
                    continue
                
                d = get_user_model().objects.get(email=request.data['doctor'])
                conflicts = SchedulerSlots.objects.filter(
                    (Q(doctor=d, schedule_start_time__lt=slot_end, schedule_end_time__gt=slot_start))
                )
                
                if conflicts.exists():
                    skipped_count += 1
                    continue
                
                user_type = get_cu_user_type(get_user_model().objects.filter(email=request.data['doctor']).first().id)
                if user_type not in ['doctor', 'researcher', 'influencer']:
                    return JsonResponse({"message": "Only experts can create appointment slots"})
                
                SchedulerSlots.objects.create(
                    doctor=d,
                    schedule_start_time=slot_start,
                    schedule_end_time=slot_end
                )
                created_count += 1
            
            if created_count == 0:
                return JsonResponse({"message": "No slots created. All requested slots were either invalid or already booked."})
            elif skipped_count == 0:
                return JsonResponse({"message": f"All {created_count} slots created successfully."})
            else:
                return JsonResponse({"message": f"{created_count} slots created successfully. {skipped_count} slots were skipped due to conflicts or invalid duration."})

        elif request.data['recurrence'] == 2:
            try:
                start_date = parse_datetime(request.data['start_date']).date()
                end_date = parse_date(request.data['end_date'])
                weekday_num = request.data['week_day']
                weekday_name = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

                if weekday_num < 1 or weekday_num > 7:
                    return JsonResponse({"message": "Invalid weekday specified"}, status=400)

                selected_day = weekday_name[weekday_num - 1]
                doctor = get_user_model().objects.get(email=request.data['doctor'])
                user_type = get_cu_user_type(doctor.id)

                if user_type not in ['doctor', 'researcher', 'influencer']:
                    return JsonResponse({"message": "Only experts can create appointment slots"})

                date_arr, created, skipped, conflicts = [], 0, 0, []

                while start_date <= end_date:
                    if start_date.strftime('%A') == selected_day:
                        slot_start = timezone.make_aware(
                            parse_datetime(f"{start_date}T{request.data['schedule_start_time']}"))
                        slot_end = timezone.make_aware(
                            parse_datetime(f"{start_date}T{request.data['schedule_end_time']}"))
                        date_arr.append((slot_start, slot_end))
                    start_date += timedelta(days=1)

                if not date_arr:
                    return JsonResponse({"message": "Please select a valid date range."}, status=400)

                for slot_start, slot_end in date_arr:
                    duration = check_if_slot_time_valid(slot_start.strftime('%Y-%m-%d %H:%M'),
                                                        slot_end.strftime('%Y-%m-%d %H:%M'))
                    if duration not in [30, 60, 90, 120, 150, 180]:
                        skipped += 1
                        continue

                    if SchedulerSlots.objects.filter(
                        doctor=doctor,
                        schedule_start_time__lt=slot_end,
                        schedule_end_time__gt=slot_start
                    ).exists():
                        skipped += 1
                        conflicts.append(slot_start.strftime('%Y-%m-%d'))
                        continue

                    SchedulerSlots.objects.create(
                        doctor=doctor,
                        schedule_start_time=slot_start,
                        schedule_end_time=slot_end
                    )
                    created += 1

                if created == 0:
                    msg = "No slots created."
                    if conflicts:
                        msg += f" Conflicts on: {', '.join(conflicts)}."
                    return JsonResponse({"message": msg}, status=400)

                msg = f"{created} slots created."
                if skipped:
                    msg += f" {skipped} skipped."
                    if conflicts:
                        msg += f" Conflicts on: {', '.join(conflicts)}."
                return JsonResponse({"message": msg})

            except Exception as e:
                import traceback
                print(f"Error: {e}\n{traceback.format_exc()}")
                return JsonResponse({"message": f"Error: {str(e)}"}, status=400)

        elif request.data['recurrence'] == 0:
            start_date = parse_datetime(request.data['start_date'])
            time1 = str(start_date.date()) + "T" + \
                request.data['schedule_start_time']
            time2 = str(start_date.date()) + "T" + \
                request.data['schedule_end_time']

            r1 = check_if_slot_time_valid(time1, time2)
            if r1 in [30, 60, 90, 120, 150, 180]:
                res = check_if_slot_alreadybooked(
                    {"schedule_start_time": time1, "schedule_end_time": time2, "doctor": request.data['doctor']})
                if len(res) > 0:
                    raise APIException("Slot already booked")
                else:
                    a = get_cu_user_type(get_user_model().objects.filter(
                        email=request.data['doctor']).first().id)
                    if a in ['doctor', 'researcher', 'influencer']:

                        res1 = self.create(request, *args, **kwargs)
                        print(f"res---{res1}")

                        return res1
                    else:
                        return JsonResponse({"message": "only experts can create appointment slots"})
            else:
                raise APIException("Invalid time slot")

        elif request.data['recurrence'] == 3:
            start_date = parse_datetime(request.data['start_date'])
            end_date = parse_datetime(request.data['end_date'])
            timezone_name = request.data.get('timezone', 'UTC')
            user_timezone = zoneinfo.ZoneInfo(timezone_name)

            recurrence_day = request.data.get('recurrence_day')
            if recurrence_day is not None:
                recurrence_day = int(recurrence_day)
            else:
                recurrence_day = start_date.day  # fallback to start_date.day

            current_date = start_date
            created_count = 0
            skipped_count = 0

            while current_date <= end_date:
                year = current_date.year
                month = current_date.month

                last_day = monthrange(year, month)[1]
                day = min(recurrence_day, last_day)
                slot_date = datetime(year, month, day).date()

                # Ensure we're not skipping valid first slot
                if slot_date < start_date.date():
                    current_date += relativedelta(months=1)
                    continue
                if slot_date > end_date.date():
                    break

                start_date_raw = f"{slot_date}T{request.data['schedule_start_time']}"
                end_date_raw = f"{slot_date}T{request.data['schedule_end_time']}"

                naive_start = parse_datetime(start_date_raw)
                naive_end = parse_datetime(end_date_raw)

                slot_start = naive_start.replace(tzinfo=user_timezone)
                slot_end = naive_end.replace(tzinfo=user_timezone)

                slot_start_str = slot_start.strftime('%Y-%m-%d %H:%M')
                slot_end_str = slot_end.strftime('%Y-%m-%d %H:%M')

                duration = check_if_slot_time_valid(slot_start_str, slot_end_str)
                if duration not in [30, 60, 90, 120, 150, 180]:
                    skipped_count += 1
                    current_date += relativedelta(months=1)
                    continue

                conflicts = check_if_slot_alreadybooked_rec(slot_start_str, slot_end_str, request.data['doctor'])
                if conflicts:
                    skipped_count += 1
                    current_date += relativedelta(months=1)
                    continue

                user = get_user_model().objects.filter(email=request.data['doctor']).first()
                user_type = get_cu_user_type(user.id)
                if user_type not in ['doctor', 'researcher', 'influencer']:
                    return JsonResponse({"message": "Only experts can create appointment slots"})

                SchedulerSlots.objects.create(
                    doctor=user,
                    schedule_start_time=slot_start,
                    schedule_end_time=slot_end,
                    timezone=timezone_name
                )
                created_count += 1
                current_date += relativedelta(months=1)

            if created_count == 0:
                return JsonResponse({"message": "No slots created. All requested monthly slots were either invalid or already booked."})
            elif skipped_count == 0:
                return JsonResponse({"message": f"All {created_count} monthly slots created successfully."})
            else:
                return JsonResponse({"message": f"{created_count} monthly slots created successfully. {skipped_count} slots were skipped due to conflicts or invalid duration."})

        else:
            pass


@method_decorator(csrf_exempt, name="dispatch")
# get slots on a day
class GetDaySlots(View):
    def get(self, r):
        # req_date=datetime.strptime(r.GET['dateSelected'], "%Y-%m-%d")
        time1 = parse_datetime(r.GET['dateSelected'])
        req_date = timezone.make_aware(time1, timezone.get_current_timezone())
        d = get_cu_user(r.GET['doctor'])

        a = SchedulerSlots.objects.filter(Q(appointments__isnull=True, doctor__exact=d) | Q(
            doctor__exact=d, appointments__status__exact="C"))
        a = a.filter(~Q(status__exact="C"))
        date_filtered_data = []
        for x in a:
            if x.schedule_start_time.date() == req_date.date() and x.status == None:

                t_slot = x.id, timezone.localtime(x.schedule_start_time, timezone.get_current_timezone(
                )), timezone.localtime(x.schedule_end_time, timezone.get_current_timezone())
                print(f'in loop----{t_slot}')
                date_filtered_data.append(list(t_slot))

        print(
            f"getdayslots------{date_filtered_data}----{len(date_filtered_data)}--")
        return JsonResponse({"available_slots": date_filtered_data})


@method_decorator(csrf_exempt, name="dispatch")
class GetAllSlots(View):
    def get(self, r):
        pass


# def add_session_details(ApptId):
#     a = MeetingSession.objects.create(AppointmentId=ApptId)
#     a.SessionId = ''.join(random.choice('**********ABCDEF') for i in range(16))
#     a.MeetingId = random.sample(range(1, 10000), 1)[0]
#     a.MeetingPwd = ''.join(random.choice('**********') for i in range(8))
#     a.save()
#     print(f"meeting session--{a}")
#     return a
def add_session_details(ApptId):
    """
    Create a MeetingSession for the given appointment and set an expiration time
    (1 hour after the meeting start time).
    """
    try:
        # Fetch the appointment to get the meeting start time
        appointment = Appointments.objects.get(id=ApptId.id)
        meeting_start_time = timezone.localtime(
            appointment.slot_id.schedule_start_time,
            timezone.get_current_timezone()
        )
        # Set expiration to 1 hour after meeting start time
        expiration_time = meeting_start_time + timedelta(hours=1)

        # Create meeting session
        session = MeetingSession.objects.create(
            AppointmentId=appointment,
            SessionId=''.join(random.choice('**********ABCDEF')
                              for i in range(16)),
            MeetingId=str(random.randint(1, 9999)).zfill(
                4),  # Ensure 4-digit MeetingId
            MeetingPwd=''.join(random.choice('**********') for i in range(8)),
            ExpirationTime=expiration_time  # Store expiration time
        )

        print(
            f"Created meeting session: {session.SessionId} with expiration: {session.ExpirationTime}")
        return session

    except Exception as e:
        print(f"Error creating meeting session: {str(e)}")
        return None


def send_meet_mail(app_obj):

    patient_details = app_obj.patient.name
    doctor_details = app_obj.slot_id.doctor.name
    doctor_id_val = app_obj.slot_id.doctor.id
    session_id = app_obj.meetingsession_set.all()[0].SessionId
    meeting_id = app_obj.meetingsession_set.all()[0].MeetingId
    meeting_pw = app_obj.meetingsession_set.all()[0].MeetingPwd
    meeting_start_time, meeting_end_time = (timezone.localtime(app_obj.slot_id.schedule_start_time, timezone.get_current_timezone()),
                                            timezone.localtime(app_obj.slot_id.schedule_end_time, timezone.get_current_timezone()))

    print(f'--------meeting_start_time--------{meeting_start_time}')
    print(f'--------meeting_end_time--------{meeting_end_time}')
    meeting_link1 = os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + "/consentFormPage?patientName=" + quote(patient_details + "&sessionID=" +
                                                                                                           session_id + "&role=0&appointmentId="+str(app_obj.id)+"&doctorName="+doctor_details+"&doctor_id="+str(doctor_id_val), safe='/&=')
    meeting_link2 = os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + "/consentFormPage?patientName=" + quote(patient_details + "&sessionID=" + session_id +
                                                                                                          "&role=1&appointmentId="+str(app_obj.id)+"&doctorName="+doctor_details+"&appointment_date="+str(meeting_start_time.date()), safe='/&=-')

    payload1 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.c361edf0-e2bf-11ee-adf0-525400674725.18e41e2ea4f",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": app_obj.patient.email,
                    "name": patient_details
                }
            }

        ],
        "merge_info": {
            "p_name": patient_details,
            "d_name": doctor_details,
            "session_id": session_id,
            "meeting_pw": meeting_pw,
            "meeting_start_time": meeting_start_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_end_time": meeting_end_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_link": meeting_link1,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    payload2 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.373962d0-e2c0-11ee-adf0-525400674725.18e41e5e17d",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": app_obj.slot_id.doctor.email,
                    "name": doctor_details
                }
            }

        ],
        "merge_info": {
            "p_name": patient_details,
            "d_name": doctor_details,
            "session_id": session_id,
            "meeting_pw": meeting_pw,
            "meeting_start_time": meeting_start_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_end_time": meeting_end_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_link": meeting_link2,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:

        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload1))
        print(
            f"verify mail send111-----------{meeting_link1}-----------{meeting_link2}--------{response.status_code}-------{response.content}--------")
        response1 = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload2))
        print(
            f"verify mail send222-----------{response1.status_code}-------{response1.content}--------")

        response_status1 = True if response.status_code == 200 else True if response.status_code in [
            201, 202] else False
        response_status2 = True if response1.status_code == 200 else True if response1.status_code in [
            201, 202] else False
        return response_status1 and response_status2
    except HTTPError as e:
        print(f"meet mail exception-----------{e.to_dict}")
        return response_status1 and response_status2


def SendPushNotification(doctor_id, patient_name):
    device = FCMDevice.objects.filter(user_id__exact=doctor_id)

    u_name = get_user_model().objects.get(id__exact=doctor_id).name
    print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT+"\logo.png"
    current_time = timezone.now()
    a = device.send_message(Message(notification=Notification(title='Appointment scheduled!', body=f'Hi {u_name}, An appointment has been fixed with {patient_name}!!'), webpush=WebpushConfig(
        fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')+'/appointment'))))
    res = PushNotifications.objects.create(UserId=doctor_id, NotificationTime=current_time,
                                           Title='Appointment scheduled!', Body=f'Hi {u_name}, An appointment has been fixed with {patient_name}!!', Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')+'/appointment')
    print(f"push----{a}")
    return a


class ValidateMeetingSessionView(views.APIView):
    """
    API to validate a meeting session's expiration and details.
    Used by Next.js frontend to check if a meeting link is valid.
    """

    def post(self, request):
        try:
            session_id = request.data.get('sessionID')
            meeting_id = request.data.get('meetingId')
            appointment_id = request.data.get('appointmentId')

            # Validate required fields
            if not all([session_id, meeting_id, appointment_id]):
                return JsonResponse({"error": "Missing required parameters"}, status=400)

            # Fetch meeting session
            session = MeetingSession.objects.filter(
                SessionId=session_id,
                MeetingId=meeting_id,
                AppointmentId_id=appointment_id
            ).first()

            if not session:
                return JsonResponse({"error": "Invalid session or appointment"}, status=404)

            # Check expiration
            current_time = timezone.now()
            if current_time > session.ExpirationTime:
                return JsonResponse({"message": "This meeting link has expired"}, status=403)

            # Return session details if valid
            return JsonResponse({
                "status": "valid",
                "session_id": session.SessionId,
                "meeting_id": session.MeetingId,
                "appointment_id": session.AppointmentId_id,
                "expiration_time": session.ExpirationTime.strftime('%Y-%m-%d %H:%M')
            })

        except Exception as e:
            print(f"Error validating meeting session: {str(e)}")
            return JsonResponse({"error": "An error occurred"}, status=500)


class GetUserAppointments(generics.ListAPIView):
    queryset = Appointments.objects.all()
    serializer_class = AppointmentsSerializer
    lookup_field = 'id'

    def get_queryset(self):
        id_val = self.kwargs['id']
        user_role = get_cu_user_type(id_val)
        f = Appointments.objects.all()
        if user_role == "patient":
            f = f.filter(patient__id=id_val)
            if "name" in self.request.GET and self.request.GET['name'] != '':
                try:
                    name_id = int(self.request.GET['name'])
                    f = f.filter(slot_id__doctor__id__exact=name_id) | f.filter(
                        slot_id__doctor__name__icontains=self.request.GET['name'])
                except ValueError:
                    f = f.filter(
                        slot_id__doctor__name__icontains=self.request.GET['name'])
                print(f'------app_name_filter-----{f}')

        elif user_role == "doctor" or user_role == "researcher" or user_role == "influencer":
            f = f.filter(slot_id__doctor__exact=id_val)
            if "name" in self.request.GET and self.request.GET['name'] != '':
                try:
                    name_id = int(self.request.GET['name'])
                    f = f.filter(patient__id__icontains=self.request.GET['name']) | f.filter(
                        patient__name__icontains=self.request.GET['name'])
                except ValueError:
                    f = f.filter(
                        patient__name__icontains=self.request.GET['name'])
                print(f'------app_name_filter-----{f}')

        else:
            f = []

        if "status" in self.request.GET and self.request.GET['status'] != '':
            if self.request.GET['status'] == "Ongoing":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=1, AppointmentId__status__in=['B', 'R', 'P'])
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids)
            elif self.request.GET['status'] == "Unattended":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=3, AppointmentId__status__in=['B', 'R', 'P'])
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids)
            elif self.request.GET['status'] == "Expired":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=0)
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids, slot_id__schedule_end_time__lte=timezone.now(
                ), status__in=["B", "R"])
            elif self.request.GET['status'] == "Upcoming":
                f = f.filter(
                    status__in=["B", "R"], slot_id__schedule_start_time__gte=timezone.now())
            elif self.request.GET['status'] == "Completed":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=2, AppointmentId__status__in=['B', 'R', 'P'])
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids)
            elif self.request.GET['status'] == "Rescheduled":
                f = f.filter(status__exact="R",
                             slot_id__schedule_start_time__gte=timezone.now())
            elif self.request.GET['status'] == "Cancelled":
                f = f.filter(status__exact="C")
            elif self.request.GET['status'] == "Cancellation Rejected":
                f = f.filter(status__exact="C_R")
            elif self.request.GET['status'] == "Cancellation Pending":
                f = f.filter(status__exact="C_P")
            else:
                print("-----------not a valid appointment status----------")
            print(f'-----appointment_status_filter{f}')

        if "start_date" in self.request.GET and self.request.GET['start_date'] != '':
            start_date = timezone.make_aware(parse_datetime(self.request.GET['start_date']),
                                             timezone.get_current_timezone())
            f = f.filter(slot_id__schedule_start_time__gte=start_date)
            print(f'-----------app_start_date_filter-----{f}')

        if "end_date" in self.request.GET and self.request.GET['end_date'] != '':
            end_date = timezone.make_aware(parse_datetime(self.request.GET['end_date']),
                                           timezone.get_current_timezone())
            f = f.filter(slot_id__schedule_end_time__lte=end_date)
            print(f'-----------app_end_date_filter-----{f}')
        return f.order_by('-id')

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        if 'page' in request.GET and request.GET['page'] != "":
            page_number = request.GET.get('page', 1)
            items_per_page = request.GET.get('per_page', 10)
            total_items = len(res.data)
            paginator = Paginator(res.data, items_per_page)
            print(paginator)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            res.data = paginator.page(page_number)

        app_data_items = []
        for x in res.data:
            slot = SchedulerSlots.objects.filter(
                id__exact=x['slot_id']).first()
            if not slot:
                continue

            doc = slot.doctor.id
            patient = CuUser.objects.filter(id__exact=x['patient']).first()
            if not patient:
                continue

            # Doctor availability check
            d_a_slots = SchedulerSlots.objects.filter(
                doctor_id__exact=doc).exclude(status__exact="C")
            d_a_x = []
            for d in d_a_slots:
                if Appointments.objects.filter(slot_id_id=d.id).exists():
                    d_a_x.append('0')
                else:
                    if (d.schedule_start_time - timezone.now()).days >= 0:
                        d_a_x.append('1')
                    else:
                        d_a_x.append('0')
            x['doctor_available_slot'] = 1 if "1" in d_a_x else 0

            # Prescriptions
            user_role = get_cu_user_type(doc)
            if user_role in ['researcher', 'influencer']:
                pres = Appointments.objects.filter(
                    id__exact=x['id']).first().irprescription_set.all()
            elif user_role == 'doctor':
                pres = Appointments.objects.filter(
                    id__exact=x['id']).first().prescription_set.all()
            else:
                pres = []
            pres_ids = [z.id for z in pres]

            # Cancel reason
            cancel_reason = AppointmentMgmt.objects.filter(
                AppId_id__exact=x['id'], EventType__exact="Cancelled").first()
            x['appointment_cancel_reason'] = cancel_reason.AppointmentReason if cancel_reason else ""

            # Patient queries
            p_queries = Appointments.objects.filter(
                id__exact=x['id']).first().patientqueries_set.all()
            p_queries_list1 = ['0', '0']
            if p_queries.exists():
                p_queries_list1[0] = "1"
                if PatientQueries.objects.filter(ReplyTo__exact=p_queries.first().id).exists():
                    p_queries_list1[1] = "1"

            # Meeting and consent details
            meeting_session_details = Appointments.objects.filter(
                id__exact=x['id']).first().meetingsession_set.all()
            consent_details = AppointmentConsent.objects.filter(
                AppId_id__exact=x['id'])  # Changed from appointment to AppId_id

            # Time conversions
            converted_time1 = timezone.localtime(
                slot.schedule_start_time, timezone.get_current_timezone())
            converted_time2 = timezone.localtime(
                slot.schedule_end_time, timezone.get_current_timezone())

            # Patient data
            x['patient'] = serialize_model(patient, CuUserSerializer)
            p_o_data = filter_user_data(patient)
            if p_o_data['patient_other_details']['ProfilePhoto'] is not None:
                x['patient_ProfilePhoto'] = get_s3_signed_url_bykey(
                    p_o_data['patient_other_details']['ProfilePhoto'])
            if p_o_data['patient_other_details']['Signature'] is not None:
                x['patient_Signature'] = get_s3_signed_url_bykey(
                    p_o_data['patient_other_details']['Signature'])

            # Doctor data
            doctor = slot.doctor
            doctor_data = filter_user_data(doctor)
            x['doctor_id'] = doc
            x['doctor_email'] = doctor_data['email']
            x['doctor_approval'] = doctor_data['approval']
            x['doctor_name'] = doctor_data['name']
            x['expert_role'] = doctor_data['role']
            x['expert_prefix'] = doctor_data['prefix']
            x['doctor_department'] = doctor_data['doctor_other_details']['Dept']
            x['doctor_member_code'] = doctor_data['doctor_other_details']['MemberCode']
            if doctor_data['doctor_other_details']['ProfilePhoto'] is not None:
                x['doctor_ProfilePhoto'] = get_s3_signed_url_bykey(
                    doctor_data['doctor_other_details']['ProfilePhoto'])
            if doctor_data['doctor_other_details']['Signature'] is not None:
                x['doctor_Signature'] = get_s3_signed_url_bykey(
                    doctor_data['doctor_other_details']['Signature'])

            # Ratings
            Apps = Appointments.objects.filter(
                slot_id__doctor__exact=doctor.id)
            doctors_s = CuUser.objects.filter(id__exact=doctor.id)
            stories_count = 0
            reviews_count = 0
            rating_s = 0
            rating_r = 0
            for z in Apps:
                stories = z.patientstories_set.all()
                stories_count += stories.count()
                for v in stories:
                    rating_s += v.Rating
            for z in doctors_s:
                reviews = z.doctorreviews_set.all()
                for u in reviews:
                    if u.ReviewStatus == 2:
                        rating_r += u.ReviewRating
                        reviews_count += 1
            rating_in_number1 = int(
                rating_s/stories_count) if stories_count != 0 else 0
            rating_in_number2 = int(
                rating_r/reviews_count) if reviews_count != 0 else 0
            rating_in_number = (int(rating_in_number1+rating_in_number2)/2)

            # Current status
            current_status = ''
            if x['status'] in ['B', 'R']:
                if timezone.localtime(timezone.now(), timezone.get_current_timezone()).timestamp() >= timezone.localtime(slot.schedule_end_time, timezone.get_current_timezone()).timestamp():
                    if meeting_session_details and meeting_session_details[0].MeetingStatus == 0:
                        current_status = "Expired"
                    elif meeting_session_details and meeting_session_details[0].MeetingStatus == 2:
                        current_status = "Completed"
                    elif meeting_session_details and meeting_session_details[0].MeetingStatus == 1:
                        current_status = "Ongoing"
                    elif meeting_session_details and meeting_session_details[0].MeetingStatus == 3:
                        current_status = "Unattended"
                elif meeting_session_details and meeting_session_details[0].MeetingStatus == 3:
                    current_status = "Unattended"
                elif meeting_session_details and meeting_session_details[0].MeetingStatus == 2:
                    current_status = "Completed"
                else:
                    current_status = "Upcoming"
            x['current_status'] = current_status

            # Final data assembly
            x.update({
                'prescriptions': pres_ids,
                'p_queries': p_queries_list1,
                "patient_stories": stories_count,
                "doctor_reviews": reviews_count,
                "doctor_rating": rating_in_number,
                'slot_start_time': converted_time1,
                'slot_end_time': converted_time2,
                'meeting_session_details': [serialize_model(t, MeetingSessionSerializer) for t in meeting_session_details],
                'consent_details': [serialize_model(t, AppointmentConsentSerializer) for t in consent_details],
            })

            app_data_items.append(x)

        if 'page' in request.GET and request.GET['page'] != "":
            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': app_data_items
            }
            return JsonResponse(response_data)
        else:
            return JsonResponse(app_data_items, safe=False)


class StartMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()

        # Check if the meeting is expired
        current_time = timezone.now()
        if instance.ExpirationTime and current_time > instance.ExpirationTime:
            return JsonResponse(
                {"message": "This meeting has expired"},
                status=403
            )

        instance.IsDoctorPresent = True
        # instance.SessionStartTime=datetime.now()
        instance.SessionStartTime = timezone.now()
        instance.MeetingStatus = 1
        instance.save(
            update_fields=['IsDoctorPresent', 'SessionStartTime', 'MeetingStatus'])
        return instance


    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)

        # Check if res is a JsonResponse (error case)
        if isinstance(res, JsonResponse):
            return res

        # Success case: serialize the MeetingSession instance
        res_data = {
            'session_data': serialize_model(res, MeetingSessionSerializer),
            'role': 1 if self.kwargs.get('role') == "doctor" else 0
        }
        return JsonResponse(res_data)

    # def put(self, request, *args, **kwargs):

    #     res = self.partial_update(request, *args, **kwargs)
    #     res_data = dict()
    #     res_data['session_data'] = serialize_model(
    #         res, MeetingSessionSerializer)
    #     res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
    #     return JsonResponse(res_data)


class AllowJoinMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        print(f"allowwww--------{self.kwargs.get('approval')}")

        instance.PatientJoinApproval = True if self.kwargs.get(
            'approval') == 1 else False
        instance.PatientRequest = False if self.kwargs.get(
            'approval') == 1 else True
        instance.DoctorAllowPatient = 2 if self.kwargs.get(
            'approval') == 1 else 0
        instance.save(
            update_fields=['PatientJoinApproval', 'PatientRequest', 'DoctorAllowPatient'])
        return instance

    def put(self, request, *args, **kwargs):
        obj1 = self.get_object()
        if obj1.MeetingStatus == 1:
            res = self.partial_update(request, *args, **kwargs)
            res_data = dict()
            res_data['session_data'] = serialize_model(
                res, MeetingSessionSerializer)
            res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0

        else:
            return JsonResponse({"message": "meeting not yet started by doctor"})

        return JsonResponse(res_data)
        # return JsonResponse(serialize_model(res, MeetingSessionSerializer))


class JoinMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        # Check if the meeting is expired
        
        instance.IsPatientPresent = True
        # instance.PatientJoinTime=datetime.now()
        instance.PatientJoinTime = timezone.now()
        instance.save(update_fields=['IsPatientPresent', 'PatientJoinTime'])
        return instance

    def put(self, request, *args, **kwargs):
        obj1 = self.get_object()
        current_time = timezone.now()
        if obj1.ExpirationTime and current_time > obj1.ExpirationTime:
            return JsonResponse({"message": "This meeting has expired"}, status=403)
            
        if obj1.MeetingStatus == 1:
            if obj1.PatientJoinApproval == True:
                res = self.partial_update(request, *args, **kwargs)
                res_data = dict()
                res_data['session_data'] = serialize_model(
                    res, MeetingSessionSerializer)
                res_data['role'] = 1 if self.kwargs.get(
                    'role') == "doctor" else 0
            else:
                return JsonResponse({"message": "Patient not yet allowed to join by the doctor"})

        else:
            return JsonResponse({"message": "meeting not yet started by doctor"})

        return JsonResponse(res_data)


class LeaveMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.IsPatientPresent = False
        # instance.PatientLeaveTime=datetime.now()
        instance.PatientLeaveTime = timezone.now()
        instance.PatientJoinApproval = False
        instance.PatientRequest = False
        instance.save(update_fields=[
                      'PatientJoinApproval', 'PatientLeaveTime', 'IsPatientPresent', 'PatientRequest'])
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)
        res_data = dict()
        res_data['session_data'] = serialize_model(
            res, MeetingSessionSerializer)
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        return JsonResponse(res_data)


def perform_action(app_id):
    app_obj = Appointments.objects.get(id__exact=app_id)
    pq_obj = PatientQueries.objects.filter(ApptId_id__exact=app_id)
    print("in perform action")
    if pq_obj.exists():
        print("in perform_action")
        # adding for the expert transaction table
        start_time = app_obj.slot_id.schedule_start_time
        end_time = app_obj.slot_id.schedule_end_time
        f_time = (end_time-start_time).total_seconds()
        f_time = f_time/1800
        transaction_amount = f_time * app_obj.slot_id.doctor.doctordetails.ConsultationFees
        c_p = app_obj.slot_id.doctor.doctordetails.CommissionPercentage
        c_a = 0
        if c_p > 0:
            c_a = app_obj.slot_id.doctor.doctordetails.ConsultationFees - \
                app_obj.slot_id.doctor.doctordetails.ConsultationFees * c_p * 0.01
        transaction_amount = transaction_amount - c_a
        wallet_obj = ExpertWalletTransactions.objects.filter(
            WalletId__ExpertId__exact=app_obj.slot_id.doctor.id)
        wallet_exp = ExpertWallet.objects.filter(
            ExpertId_id__exact=app_obj.slot_id.doctor.id)[0]
        app_mgm = AppointmentMgmt.objects.create(AppId_id=app_id, EventType="Expert_unpaid_appointment", By=CuUser.objects.get(
            id=app_obj.slot_id.doctor.id), AppointmentReason="Expert not replied to the query")
        if wallet_obj.exists():
            wallet_obj = wallet_obj.order_by('-TransactionDate')[0]
            balance = wallet_obj.BalanceAmount
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=2, TransactionAmount=transaction_amount, CommissionAmount=c_a)
        else:
            balance = 0
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=2, TransactionAmount=transaction_amount, CommissionAmount=c_a)
    else:
        print("in perform/action")
        # adding for the expert transaction table
        start_time = app_obj.slot_id.schedule_start_time
        end_time = app_obj.slot_id.schedule_end_time
        f_time = (end_time-start_time).total_seconds()
        f_time = f_time/1800
        transaction_amount = f_time * app_obj.slot_id.doctor.doctordetails.ConsultationFees
        c_p = app_obj.slot_id.doctor.doctordetails.CommissionPercentage
        c_a = 0
        if c_p > 0:
            c_a = app_obj.slot_id.doctor.doctordetails.ConsultationFees * c_p * 0.01
        transaction_amount = transaction_amount - c_a
        wallet_obj = ExpertWalletTransactions.objects.filter(
            WalletId__ExpertId__exact=app_obj.slot_id.doctor.id)
        wallet_exp = ExpertWallet.objects.filter(
            ExpertId_id__exact=app_obj.slot_id.doctor.id)[0]
        if wallet_obj.exists():
            wallet_obj = wallet_obj.order_by('-TransactionDate')[0]
            balance = wallet_obj.BalanceAmount + transaction_amount
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=0, TransactionAmount=transaction_amount, CommissionAmount=c_a)
        else:
            balance = transaction_amount
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=0, TransactionAmount=transaction_amount, CommissionAmount=c_a)
    print("Add the action you want to perform here")

# Function to schedule the task on the third day


def schedule_task(app_id, end_time):
    third_day_time = end_time + timedelta(days=3)
    scheduler.add_job(perform_action, 'date',
                      run_date=third_day_time, args=[app_id])


class EndMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.IsPatientPresent = False
        instance.IsDoctorPresent = False
        # instance.SessionEndTime=datetime.now()
        instance.SessionEndTime = timezone.now()
        # instance.MeetingStatus=2
        # added
        if instance.PatientJoinTime is None:
            instance.MeetingStatus = 3
        else:
            instance.MeetingStatus = 2
        # ---------------------------------------
        instance.IsSuccess = 1
        instance.PatientJoinApproval = False
        instance.PatientRequest = False
        instance.DoctorAllowPatient = 1
        instance.save(update_fields=['IsPatientPresent', 'IsDoctorPresent', 'SessionEndTime',
                      'MeetingStatus', 'IsSuccess', 'PatientJoinApproval', 'PatientRequest', 'DoctorAllowPatient'])
        # added for background_job
        schedule_task(instance.AppointmentId_id, timezone.now())
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)
        res_data = dict()
        res_data['session_data'] = serialize_model(
            res, MeetingSessionSerializer)
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        return JsonResponse(res_data)


class PatientRequestSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.IsDoctorPresent == True:
            instance.PatientRequest = True
            instance.DoctorAllowPatient = 1
        instance.save(update_fields=['PatientRequest', 'DoctorAllowPatient'])
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)
        res_data = dict()
        res_data['session_data'] = serialize_model(
            res, MeetingSessionSerializer)
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        return JsonResponse(res_data)


class CheckPatientSessionApproval(generics.RetrieveAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def get(self, request, *args, **kwargs):
        res = self.retrieve(request, *args, **kwargs)
        patient_name = self.get_object().AppointmentId.patient.name

        res_data = dict()

        res_data['session_data'] = res.data
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        res_data['patient'] = patient_name
        return JsonResponse(res_data)


def app_timings(slot_id):
    slot_data = SchedulerSlots.objects.filter(id__exact=slot_id)[0]

    print(f'-----------------------------slot_data----------{slot_data}')
    start_date = slot_data.schedule_start_time
    end_date = slot_data.schedule_end_time
    converted_time1 = timezone.localtime(
        start_date, timezone.get_current_timezone())
    print(f'-------------------coverted_time---------{converted_time1}')
    converted_time2 = timezone.localtime(
        end_date, timezone.get_current_timezone())
    print(f'-------------------coverted_time---------{converted_time2}')

    s_date = converted_time1.date()
    ss_time = converted_time1.time()
    b = str(ss_time).split(":")
    s_time = b[0]+":"+b[1]

    ee_time = converted_time2.time()
    b = str(ee_time).split(":")
    e_time = b[0]+":"+b[1]

    timings = str(str(s_date) + " " + s_time + "-" + e_time)
    return timings


def AppointmentReEmail(app_id, slot_id, pre_app_slot):
    cur_timings = app_timings(slot_id)
    pre_timings = app_timings(pre_app_slot)

    app_details = Appointments.objects.filter(id__exact=app_id)[0]
    patient_details = CuUser.objects.filter(
        id__exact=app_details.patient_id)[0]
    patient_name = patient_details.name
    patient_email = patient_details.email

    doctor_id = SchedulerSlots.objects.filter(
        id__exact=app_details.slot_id_id)[0].doctor_id
    doctor_details = CuUser.objects.filter(id__exact=doctor_id)[0]
    doctor_email = doctor_details.email
    doctor_name = doctor_details.name
    expert_role = get_cu_user_type(doctor_details.id)
    payload1 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.32612990-fe19-11ee-97ae-525400ab18e6.18ef51f82a9",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": doctor_email,
                    "name": doctor_name
                }
            }

        ],
        "merge_info": {
            "user_role": "patient",
            "new_timings": cur_timings,
            "prev_timings": pre_timings,
            "aa_name": patient_name,
            "a_name": doctor_name,
            "app_id": app_id,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    payload2 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.32612990-fe19-11ee-97ae-525400ab18e6.18ef51f82a9",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": patient_email,
                    "name": patient_name
                }
            }

        ],
        "merge_info": {
            "user_role": expert_role,
            "new_timings": cur_timings,
            "prev_timings": pre_timings,
            "aa_name": doctor_name,
            "a_name": patient_name,
            "app_id": app_id,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }
    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:

        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload1))
        print(
            f"verify mail send111-------------{response.status_code}-------{response.content}--------")
        response1 = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload2))
        print(
            f"verify mail send222-----------{response1.status_code}-------{response1.content}--------")

        response_status1 = True if response.status_code == 200 else True if response.status_code in [
            201, 202] else False
        response_status2 = True if response1.status_code == 200 else True if response1.status_code in [
            201, 202] else False
        return response_status1 and response_status2
    except HTTPError as e:
        print(f"meet mail exception-----------{e.to_dict}")
        return response_status1 and response_status2
    pass

# doctor cancels appointment refund


def patient_refund(AppointmentId_id, partial_status):
    serializer_class = PatientPayment
    app_id = AppointmentId_id
    stripe.api_key = os.getenv("STRIPE_API_KEY")
    patient_payment_details = PatientPayment.objects.filter(
        AppointmentId_id__exact=app_id)
    if PatientPayment.objects.filter(AppointmentId_id__exact=app_id).exists():
        patient_payment_details = PatientPayment.objects.filter(
            AppointmentId_id__exact=app_id)[0]

        p_int = stripe.PaymentIntent.retrieve(
            patient_payment_details.PaymentIntent)
        amount = int(p_int.amount)

        if partial_status == True:
            start_time = Appointments.objects.filter(id__exact=AppointmentId_id)[
                0].slot_id.schedule_start_time
            start_time = timezone.localtime(
                start_time, timezone.get_current_timezone())
            cancel_time = AppointmentMgmt.objects.filter(
                AppId_id__exact=AppointmentId_id, EventType__exact="Cancelled")[0].EventDate
            cancel_time = timezone.localtime(
                cancel_time, timezone.get_current_timezone())
            time_diff = start_time - cancel_time
            time_diff = time_diff.days * 24
            print(f'------this is the time difference {time_diff}')
            cancel_p = AdminContentManagement.objects.filter(
                Category__exact="Cancellation Refund Policy")
            great_t = 1000
            less_t = 0
            p_r = 0
            r_id = 0
            r_id_id = 0
            for x in cancel_p:
                if x.Content['Time'] == time_diff:
                    p_r = x.Content['Refund']
                    break
                elif x.Content['Time'] > time_diff:
                    if x.Content['Time'] < great_t:
                        great_t = x.Content['Time']
                        r_id = x.id
                elif x.Content['Time'] > less_t:
                    r_id_id = x.id
                    less_t = x.Content['Time']

                else:
                    pass

            if p_r == 0:
                if great_t == 1000:
                    p_r = cancel_p.filter(id__exact=r_id_id)[
                        0].Content['Refund']
                    print(
                        f"-----------patinet refund----------{p_r}--------------")
                else:
                    p_r = cancel_p.filter(id__exact=r_id)[0].Content['Refund']
                    print(
                        f"-----------patinet refund----------{p_r}--------------")

            if RefundedPayments.objects.filter(PaymentIntent=patient_payment_details.PaymentIntent).exists():
                actual_amount = amount - RefundedPayments.objects.filter(
                    PaymentIntent=patient_payment_details.PaymentIntent)[0].AmountRefunded
                amount = int(int(actual_amount) * float(p_r*0.01))
                r_p = stripe.Refund.create(payment_intent=p_int, amount=amount)
                print(f"in paymentsssssssss-------------{r_p}")
            else:
                amount = int(int(amount) * float(p_r*0.01))
                r_p = stripe.Refund.create(payment_intent=p_int, amount=amount)
                print(f"in paymentsssssssss-------------{r_p}")

        else:
            r_p = stripe.Refund.create(payment_intent=p_int)
            print(f"in paymentsssssssss-------------{r_p}")
    else:
        r_p = "No payment done yet"

    return r_p


"""When the expert cancels the appointment the patient should get the full refund"""

logger = logging.getLogger(__name__)


class AppointmentsView(generics.UpdateAPIView):
    queryset = Appointments.objects.all()
    lookup_field = 'id'
    serializer_class = AppointmentsSerializer

    def _process_expert_cancellation_refund(self, appointment_id):
        try:
            appointment = Appointments.objects.get(id=appointment_id)
            payment = PatientPayment.objects.get(AppointmentId=appointment)
            airwallex = AirwallexService()
            payment_intent = airwallex.get_payment_intent(
                payment.PaymentIntent)
            original_amount = Decimal(payment_intent['amount'])

            refund_response = airwallex.create_refund(
                payment_intent_id=payment.PaymentIntent,
                amount=float(original_amount),
                currency=payment_intent['currency'],
                reason="Full refund due to expert cancellation"
            )

            RefundedPayments.objects.create(
                PaymentIntent=payment.PaymentIntent,
                RefundId=refund_response['id'],
                AmountRefunded=original_amount,
                RefundStatus=refund_response['status'],
                RefundCurrency=payment_intent['currency'],
                AppId=appointment,
                RefundDate=timezone.now(),
                RefundObject={
                    "refund_type": "full_refund",
                    "reason": "Expert-initiated cancellation",
                    "original_amount": float(original_amount),
                    "refund_response": refund_response
                }
            )

            return {
                "success": True,
                "refunded_amount": float(original_amount),
                "currency": payment_intent['currency'],
                "status": refund_response['status']
            }

        except Exception as e:
            logger.exception("Error during refund processing")
            return {"success": False, "message": str(e)}

    def _send_expert_cancellation_notifications(self, appointment):
        try:
            patient_id = appointment.patient_id
            doctor_id = appointment.slot_id.doctor_id
            slot_id = appointment.slot_id_id

            if notification_check('Appointment cancellation'):
                CancelPushNotification(patient_id, doctor_id)

            CancelAppAdmin(appointment.id)

            if email_check('Appointment cancellation'):
                CancelEmail(patient_id, doctor_id, slot_id,
                            appointment.id, "Expert-initiated cancellation")

            return True
        except Exception as e:
            logger.exception("Notification error during cancellation")
            return False

    def partial_update(self, request, *args, **kwargs):
        try:
            print(f"Incoming request data: {request.data}")
            appointment_id = self.kwargs.get('id')
            event_type = request.data.get('EventType')
            user_id = request.data.get('By')

            if event_type == "Cancelled":
                instance = self.get_object()
                if instance.status in ["B", "R"]:
                    user_type = get_cu_user_type(user_id)

                    if user_type == "patient":
                        reason = request.data.get("AppointmentReason")
                        if not reason:
                            return Response("Appointment cancellation reason not specified", status=400)

                        instance.status = "C_P"
                        instance.save(update_fields=['status'])

                        AppointmentMgmt.objects.create(
                            AppId=instance,
                            EventType=event_type,
                            By=get_user_model().objects.get(id=user_id),
                            EventDate=timezone.now(),
                            AppointmentReason=reason
                        )

                    else:
                        instance.status = "C"
                        instance.save(update_fields=['status'])

                        slot = SchedulerSlots.objects.get(
                            id=instance.slot_id_id)
                        slot.status = "C"
                        slot.save(update_fields=['status'])

                        AppointmentMgmt.objects.create(
                            AppId=instance,
                            EventType=event_type,
                            By=get_user_model().objects.get(id=user_id),
                            EventDate=timezone.now()
                        )

                        refund_result = self._process_expert_cancellation_refund(
                            instance.id)
                        if not refund_result.get('success'):
                            return JsonResponse({
                                "message": "Appointment cancelled but refund failed - please contact support"
                            }, status=status.HTTP_207_MULTI_STATUS)

                        self._send_expert_cancellation_notifications(instance)

                else:
                    return Response({"message": "Only booked or rescheduled appointments can be cancelled."}, status=400)

            elif event_type == 'Rescheduled':
                flag = request.data.get('s_f')
                app = Appointments.objects.get(id=appointment_id)
                prev_slot_id = app.slot_id_id

                if flag == 0:
                    slot = SchedulerSlots.objects.filter(
                        id=request.data.get('s_id')).first()
                    if slot and timezone.now() < slot.schedule_start_time:
                        if get_cu_user_type(user_id) in ['doctor', 'researcher', 'influencer']:
                            old_slot = SchedulerSlots.objects.get(
                                id=prev_slot_id)
                            old_slot.status = None  # Set to None instead of 'R' to make it available again
                            old_slot.save()

                        app.slot_id_id = slot.id
                        app.status = 'R'
                        app.save()

                        if notification_check('Appointment reschedule'):
                            SendPushAppointmentReNotification(app.id, slot.id)

                        SendPushAdminRe(app.id, slot.id)

                        if email_check('Appointment reschedule'):
                            AppointmentReEmail(app.id, slot.id, prev_slot_id)

                        AppointmentMgmt.objects.create(
                            AppId=app,
                            EventType=event_type,
                            By=get_user_model().objects.get(id=user_id),
                            EventDate=timezone.now()
                        )

                elif flag == 1:
                    start_date = request.data.get('start_date')
                    time1 = parse_datetime(
                        f"{start_date}T{request.data.get('schedule_start_time')}")
                    time2 = parse_datetime(
                        f"{start_date}T{request.data.get('schedule_end_time')}")

                    converted_time1 = timezone.make_aware(
                        time1, timezone.get_current_timezone())
                    converted_time2 = timezone.make_aware(
                        time2, timezone.get_current_timezone())

                    duration = check_if_slot_time_valid(str(time1), str(time2))
                    doctor_email = CuUser.objects.get(id=user_id).email
                    slot_conflict = check_if_slot_alreadybooked({
                        "schedule_start_time": str(time1),
                        "schedule_end_time": str(time2),
                        "doctor": doctor_email
                    })

                    if duration in [30, 60, 90, 120, 150, 180] and not slot_conflict:
                        if get_cu_user_type(user_id) in ['doctor', 'researcher', 'influencer']:
                            new_slot = SchedulerSlots.objects.create(
                                schedule_start_time=converted_time1,
                                schedule_end_time=converted_time2,
                                doctor_id=user_id,
                                timezone=timezone.get_current_timezone()
                            )

                            SchedulerSlots.objects.filter(
                                id=prev_slot_id).update(status=None)  # Set to None instead of 'R'
                            app.slot_id_id = new_slot.id
                            app.status = 'R'
                            app.save()

                            if notification_check('Appointment reschedule'):
                                SendPushAppointmentReNotification(
                                    app.id, new_slot.id)

                            SendPushAdminRe(app.id, new_slot.id)

                            if email_check('Appointment reschedule'):
                                AppointmentReEmail(
                                    app.id, new_slot.id, prev_slot_id)

                            AppointmentMgmt.objects.create(
                                AppId=app,
                                EventType=event_type,
                                By=get_user_model().objects.get(id=user_id),
                                EventDate=timezone.now()
                            )
                    else:
                        raise APIException(
                            "Slot already booked or duration invalid")

                else:
                    raise ValidationError("Invalid s_f value")

            return super().partial_update(request, *args, **kwargs)

        except Exception as e:
            logger.exception("Unhandled error in partial_update")
            return Response({"error": "Internal server error", "details": str(e)}, status=500)

    def put(self, request, *args, **kwargs):
        event_type = request.data.get('EventType')
        if not event_type:
            logger.error("Missing 'EventType' in request body")
            return Response({"error": "Missing 'EventType' in request body"}, status=400)

        # Handle optional reschedule limit
        if event_type == 'Rescheduled':
            app_r = AppointmentMgmt.objects.filter(AppId__exact=kwargs['id'])
            app_c_qs = AdminContentManagement.objects.filter(
                Category="Reschedule").order_by('-id').first()

            if app_c_qs:  # Only apply limit if set
                try:
                    app_c = int(app_c_qs.Content)
                    if app_r.count() >= app_c:
                        logger.warning(
                            f"Reschedule limit ({app_c}) reached for appointment {kwargs['id']}")
                        return Response(
                            {"error": f"You can not reschedule more than {app_c} times"},
                            status=403
                        )
                except ValueError:
                    logger.warning(
                        "Invalid reschedule limit value in AdminContentManagement")
                    # Proceed without blocking if invalid config

        # Proceed with update
        res = self.partial_update(request, *args, **kwargs)
        return res if isinstance(res, Response) else Response({"message": res})

# added


def SendPushAppointmentReNotification(app_id, slot_id):
    app_details = Appointments.objects.filter(id__exact=app_id)
    slot_details = SchedulerSlots.objects.filter(id__exact=slot_id)
    if app_details.exists() and slot_details.exists():
        patient_id = Appointments.objects.filter(id__exact=app_id)[
            0].patient_id
        doctor_id = SchedulerSlots.objects.filter(
            id__exact=slot_id)[0].doctor_id
        start_time = SchedulerSlots.objects.filter(
            id__exact=slot_id)[0].schedule_start_time
        end_time = SchedulerSlots.objects.filter(id__exact=slot_id)[
            0].schedule_end_time
        start_time1 = timezone.localtime(
            start_time, timezone.get_current_timezone())
        print(f'-------------------coverted_time---------{start_time1}')
        end_time1 = timezone.localtime(
            end_time, timezone.get_current_timezone())
        print(f'-------------------coverted_time---------{end_time1}')
        s_date = start_time1.date()
        ss_time = start_time1.time()
        b = str(ss_time).split(":")
        s_time = b[0]+":"+b[1]

        ee_time = end_time1.time()
        b = str(ee_time).split(":")
        e_time = b[0]+":"+b[1]
        d_name = get_user_model().objects.get(id__exact=doctor_id).name
        p_name = get_user_model().objects.get(id__exact=patient_id).name
        # notification to doctor
        device_d = FCMDevice.objects.filter(user_id__exact=doctor_id)
        print(f"fcm devices----{device_d}---{type(device_d)}")
        img_url = settings.MEDIA_ROOT+"\logo.png"
        current_time = timezone.now()
        a = device_d.send_message(Message(notification=Notification(title='Appointment rescheduled!', body=f'Hi {d_name}, Appointment with {p_name} reschedueled to {s_date} {s_time}-{e_time}!'), webpush=WebpushConfig(
            fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')+'/appointment'))))
        res = PushNotifications.objects.create(UserId=doctor_id, NotificationTime=current_time,
                                               Title='Appointment rescheduled!', Body=f'Hi {d_name}, Appointment with {p_name} reschedueled to {s_date} {s_time}-{e_time}!', Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')+'/appointment')
        print(f"push----{a}")

        # notification to patient
        device_p = FCMDevice.objects.filter(user_id__exact=patient_id)
        print(f"fcm devices----{device_p}---{type(device_p)}")
        img_url = settings.MEDIA_ROOT+"\logo.png"
        current_time = timezone.now()
        a = device_p.send_message(Message(notification=Notification(title='Appointment rescheduled!', body=f'Hi {p_name}, Appointment with {d_name} reschedueled to {s_date} {s_time}-{e_time}!'), webpush=WebpushConfig(
            fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP')+'myprofile?tab=appointments'))))
        res = PushNotifications.objects.create(UserId=patient_id, NotificationTime=current_time,
                                               Title='Appointment rescheduled!', Body=f'Hi {p_name}, Appointment with {d_name} reschedueled to {s_date} {s_time}-{e_time}!', Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP')+'/myprofile?tab=appointments')
        print(f"push----{a}")

        return a

# added


def SendPushAdminRe(app_id, slot_id):
    app_details = Appointments.objects.filter(id__exact=app_id)
    slot_details = SchedulerSlots.objects.filter(id__exact=slot_id)
    if app_details.exists() and slot_details.exists():
        start_time = SchedulerSlots.objects.filter(
            id__exact=slot_id)[0].schedule_start_time
        end_time = SchedulerSlots.objects.filter(id__exact=slot_id)[
            0].schedule_end_time
        start_time1 = timezone.localtime(
            start_time, timezone.get_current_timezone())
        print(f'-------------------coverted_time---------{start_time1}')
        end_time1 = timezone.localtime(
            end_time, timezone.get_current_timezone())
        print(f'-------------------coverted_time---------{end_time1}')
        s_date = start_time1.date()
        ss_time = start_time1.time()
        b = str(ss_time).split(":")
        s_time = b[0]+":"+b[1]

        ee_time = end_time1.time()
        b = str(ee_time).split(":")
        e_time = b[0]+":"+b[1]

        # notification to super_admin
        admin_obj = CuUser.objects.filter(is_admin__exact=1)
        for y in admin_obj:

            device_d = FCMDevice.objects.filter(user_id__exact=y.id)
            print(f"fcm devices----{device_d}---{type(device_d)}")
            img_url = settings.MEDIA_ROOT+"\logo.png"
            current_time = timezone.now()
            a = device_d.send_message(Message(notification=Notification(title='Appointment rescheduled!', body=f'Hi {y.name}, Appointment with id -{app_id} has been reschedueled to {s_date} {s_time}-{e_time}!'), webpush=WebpushConfig(
                fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'))))
            res = PushNotifications.objects.create(UserId=y.id, NotificationTime=current_time,
                                                   Title='Appointment rescheduled!', Body=f'Hi {y.name}, Appointment with id -{app_id} has been reschedueled to {s_date} {s_time}-{e_time}!', Link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar')
            print(f"push----{a}")
    return True

# added


# added

# class my_webhook_view(views.APIView):
#     permission_classes=[]
#     def post(self,request):
#       print(f"listening--------------------------{request.META}")
#       stripe.api_key = os.getenv("STRIPE_API_KEY")
#       endpoint_secret =os.getenv("STRIPE_WEBHOOK_ENDPOINT_SECRET")
#       payload = request.body
#       sig_header = request.META['HTTP_STRIPE_SIGNATURE']
#       event = None
#       try:

#         try:
#             event = stripe.Webhook.construct_event(
#                 payload, sig_header, endpoint_secret,
#             )
#         except Exception as e:
#             # Invalid payload
#             print(f"valuerrorrrrrrrrrrrrrrrrr{e}")
#             return HttpResponse(status=400)
#         except stripe.error.SignatureVerificationError as e:
#             # Invalid signature
#             print(f"signature          errorrrrrrrrrrrrrrrrr{e}")
#             return HttpResponse(status=400)

#         # Handle the checkout.session.completed event
#         if event['type'] == 'checkout.session.completed':
#             # Retrieve the session. If you require line items in the response, you may include them by expanding line_items.
#             session = event['data']['object']
#             print(f"sessioniddddddddddddddd{session.id}")

#             if session.payment_status == "paid":
#                 AppointmentId = CreateOrder(session.id)
#                 FulfillOrder(AppointmentId,session.id)

#                 # Passed signature verification

#             return HttpResponse(status=200)

#         if event['type'] =='charge.failed':
#             failed_url = "https://docs.stripe.com/api/checkout/sessions/list"
#             print(f"----------------------------------Charge Failed")
#             session = event['data']['object']
#             stripe.checkout.Session.expire(session)
#             #p_f = event['data']['object']
#             #print(f'--------------payment failed response {p_f}-----------')
#             #return redirect(failed_url)
#             #return HttpResponse(status=200)

#         if event['type'] == 'charge.refunded':
#             print("A refund happened")
#             refund = event['data']['object']
#             print(f'--------------refund response {refund}-----------')
#             print(f'--------------refund response {refund.status}-----------')
#             print(f'--------------refund response {refund.payment_intent}-----------')
#             print(f'--------------refund response {refund.amount_refunded}-----------')
#             if RefundedPayments.objects.filter(PaymentIntent=refund.payment_intent).exists():
#                 refund_update = RefundedPayments.objects.filter(PaymentIntent=refund.payment_intent)[0]
#                 refund_id=[]
#                 refund_r = stripe.Refund.list(payment_intent=refund.payment_intent)
#                 refund_initial = refund_update.RefundObject
#                 for x in refund_r['data']:
#                     refund_id.append(x['id'])
#                 for element in refund_id:
#                     if element not in refund_initial:
#                         refund_initial.append(element)
#                         break
#                 refund_update.RefundObject=refund_initial
#                 refund_update.AmountRefunded = refund.amount_refunded
#                 refund_update.RefundDate = timezone.now()
#                 refund_update.save()

#             else:
#                 refund_id=[]
#                 refund_r = stripe.Refund.list(payment_intent=refund.payment_intent)
#                 for x in refund_r['data']:
#                     refund_id.append(x['id'])
#                 app_id=PatientPayments.objects.get(PaymentIntent__exact=refund.payment_intent).AppointmentId_id
#                 print(f'-------this is appointment id-------{app_id}----------')
#                 refunded_insert = RefundedPayments.objects.create(PaymentIntent=refund.payment_intent, AmountRefunded=refund.amount_refunded, RefundStatus=refund.status,RefundObject=refund_id,AppId_id=app_id,RefundCurrency=refund.currency)
#                 n_rc = notification_check('Refund credit')
#                 if n_rc==True:
#                     r_p = RefundPushNotification(refund.payment_intent, refund.status, refund.amount_refunded)
#                     print(f'----------- refund push response-------{r_p}------------')
#                 e_rc = email_check('Refund credit')
#                 if e_rc==True:
#                     r_e = RefundEmail(refund.payment_intent, refund.status, refund.amount_refunded)
#             return HttpResponse(status=200)
#       except Exception as e:
#           print(f'---------exception error {e}--')
#           return HttpResponse(status=500)

AIRWALLEX_WEBHOOK_SECRET = os.getenv("AIRWALLEX_WEBHOOK_SECRET")


class MyWebhookView(views.APIView):
    permission_classes = []

    def post(self, request):
        try:
            # Read and parse the webhook payload
            payload = request.body.decode('utf-8')
            event = json.loads(payload)
            print(f"Received webhook event: {event}")

            # Verify the webhook signature (optional, add your verification logic here)
            received_signature = request.META.get('HTTP_AIRWALLEX_SIGNATURE')
            if not self.verify_signature(payload, received_signature, AIRWALLEX_WEBHOOK_SECRET):
                print("Signature verification failed.")
                return HttpResponse(status=400)

            # Handle different event types
            event_type = event.get("event_type")
            event_data = event.get("data", {})
            print(f"Event type: {event_type}")

            if event['name'] == "payment_intent.succeeded":
                # session = event_data
                # print(f"Payment intent succeeded: {session.get('id')}")

                # # Perform necessary actions for a successful payment
                print(
                    f"dataaaaaaaaaaaaa------------webhook-----------{event['data']['object']['id']}")
                AppointmentId = CreateOrder(event['data']['object']['id'])
                FulfillOrder(AppointmentId, event['data']['object']['id'])
                success_url = "https://healthunwired.com/paymentsuccess"
                return redirect(success_url)

            elif event['name'] == "beneficiary.created":
                # session = event_data
                # print(f"Payment intent succeeded: {session.get('id')}")

                # # Perform necessary actions for a successful payment
                print(
                    f"dataaaaaaaaaaaaa---created----benficiary-----------webhook-----------{event['data']}")

            elif event['name'] == "beneficiary.deleted":
                # session = event_data
                # print(f"Payment intent succeeded: {session.get('id')}")

                # # Perform necessary actions for a successful payment
                print(
                    f"dataaaaaaaaaaaaa----deleted---beneficiary--------webhook-----------{event['data']}")

            elif event_type == "payment_intent.failed":
                print("Payment intent failed.")
                # Handle failed payments (e.g., notify user, log the issue)
                return HttpResponse(status=200)

            elif event_type == "refund.succeeded":
                print("Refund succeeded.")
                refund = event_data
                payment_intent_id = refund.get("payment_intent_id")
                amount_refunded = refund.get("amount")
                refund_status = refund.get("status")
                refund_currency = refund.get("currency")
                refund_id = refund.get("id")

                if RefundedPayments.objects.filter(PaymentIntent=payment_intent_id).exists():
                    # Update existing refund record
                    refund_update = RefundedPayments.objects.filter(
                        PaymentIntent=payment_intent_id)[0]
                    refund_initial = refund_update.RefundObject or []
                    if refund_id not in refund_initial:
                        refund_initial.append(refund_id)
                    refund_update.RefundObject = refund_initial
                    refund_update.AmountRefunded = amount_refunded
                    refund_update.RefundDate = timezone.now()
                    refund_update.save()
                else:
                    # Create a new refund record
                    app_id = PatientPayment.objects.get(
                        PaymentIntent__exact=payment_intent_id).AppointmentId_id
                    RefundedPayments.objects.create(
                        PaymentIntent=payment_intent_id,
                        AmountRefunded=amount_refunded,
                        RefundStatus=refund_status,
                        RefundObject=[refund_id],
                        AppId_id=app_id,
                        RefundCurrency=refund_currency,
                    )

                # Optional: Notify via push or email
                if notification_check('Refund credit'):
                    RefundPushNotification(
                        payment_intent_id, refund_status, amount_refunded)
                if email_check('Refund credit'):
                    RefundEmail(payment_intent_id,
                                refund_status, amount_refunded)

                return HttpResponse(status=200)

            else:
                print(f"Unhandled event type: {event_type}")
                return HttpResponse(status=200)

        except Exception as e:
            print(f"Webhook processing error: {e}")
            return HttpResponse(status=500)

    @staticmethod
    def verify_signature(payload, received_signature, secret):
        """
        Verify webhook signature using Airwallex's recommended method.
        This is optional, implement as per your security needs.
        """
        # Implement Airwallex signature verification logic if required
        # For now, assuming always valid
        return True


def RefundEmail(PaymentIntent, RefundStatus, RefundAmount):
    pp_details = PatientPayment.objects.filter(
        PaymentIntent__exact=PaymentIntent)[0]

    patient_id = pp_details.PatientId_id
    patient_name = CuUser.objects.filter(id__exact=patient_id).name
    patient_email = CuUser.objects.filter(id__exact=patient_id).email

    app_id = pp_details.AppId_id
    slot_id = Appointments.objects.filter(id__exact=app_id)[0].slot_id_id
    slot_data = SchedulerSlots.objects.filter(id__exact=slot_id)[0]
    print(f'-----------------------------slot_data----------{slot_data}')

    # doctor_id=slot_data.doctor_id
    doctor_name = CuUser.objects.filter(id__exact=slot_data.doctor_id).name
    start_date = slot_data.schedule_start_time
    end_date = slot_data.schedule_end_time
    converted_time1 = timezone.localtime(
        start_date, timezone.get_current_timezone())
    print(f'-------------------coverted_time---------{converted_time1}')
    converted_time2 = timezone.localtime(
        end_date, timezone.get_current_timezone())
    print(f'-------------------coverted_time---------{converted_time2}')

    s_date = converted_time1.date()
    ss_time = converted_time1.time()
    b = str(ss_time).split(":")
    s_time = b[0]+":"+b[1]

    ee_time = converted_time2.time()
    b = str(ee_time).split(":")
    e_time = b[0]+":"+b[1]

    timings = str(str(s_date) + " " + s_time + "-" + e_time)

    payload2 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.e4775f50-fe19-11ee-97ae-525400ab18e6.18ef52411c5",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": patient_email,
                    "name": patient_name
                }
            }

        ],
        "merge_info": {
            "expert_name": doctor_name,
            "p_name": patient_name,
            "app_timings": timings,
            "app_id": app_id,
            "amount": RefundAmount,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }
    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:

        response1 = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload2))
        response_status2 = True if response1.status_code == 200 else True if response1.status_code in [
            201, 202] else False
        print(
            f"verify mail send111-------------{response1.status_code}-------{response1.content}--------")
        return response_status2
    except HTTPError as e:
        print(f"meet mail exception-----------{e.to_dict}")
        return response_status2


def RefundPushNotification(PaymentIntent, RefundStatus, RefundAmount):
    current_time = timezone.now()
    u_id = PatientPayment.objects.filter(
        PaymentIntent__exact=PaymentIntent)[0].PatientId_id
    device = FCMDevice.objects.filter(user_id__exact=u_id)
    print(f"in reminderrrrrrrrrrr{device}")
    u_name = get_user_model().objects.get(id__exact=u_id).name
    print(f"in user nameeeeeeeeeeeeeeeeeeeee{u_name}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    a = device.send_message(Message(notification=Notification(title='Refund credited!',
                                                              body=f'Hi {u_name}, Your refund for amount {RefundAmount} has been credited.'),
                                    webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                        link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'))))
    res = PushNotifications.objects.create(UserId=u_id, NotificationTime=current_time,
                                           Title='Refund credited!',
                                           Body=f'Hi {u_name}, Your refund for amount {RefundAmount} has been credited.', Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments')
    return a


def AddCheckOutSessionDetails(a, b, c, d=None):
    r = PatientPayment.objects.create(
        CheckOutSessionId=a, PaymentIntent=b, PatientId=get_cu_user(int(c)), AppointmentId=d)
    if r is not None:
        return True
    else:
        return False


# added
def SendAdminAppCre(app_id, d_name, p_name):
    # notification to super_admin
    admin_obj = CuUser.objects.filter(is_admin__exact=1)
    for y in admin_obj:

        device_d = FCMDevice.objects.filter(user_id__exact=y.id)
        print(f"fcm devices----{device_d}---{type(device_d)}")
        img_url = settings.MEDIA_ROOT+"\logo.png"
        current_time = timezone.now()
        a = device_d.send_message(Message(notification=Notification(title='Appointment scheduled!', body=f'Hi {y.name}, Appointment with id -{app_id} has been schedueled between expert {d_name} and patient {p_name}!'), webpush=WebpushConfig(
            fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'))))
        res = PushNotifications.objects.create(UserId=y.id, NotificationTime=current_time,
                                               Title='Appointment scheduled!', Body=f'Hi {y.name}, Appointment with id -{app_id} has been schedueled between expert {d_name} and patient {p_name}!', Link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar')
        print(f"push----{a}")
    return True


def SendPushReminder(u_id, uu_name, days, current_time, user_role, start_time, end_time):

    device = FCMDevice.objects.filter(user_id__exact=u_id)
    print(f"in reminderrrrrrrrrrr{device}")
    u_name = get_user_model().objects.get(id__exact=u_id).name
    print(f"in user nameeeeeeeeeeeeeeeeeeeee{u_name}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    data = ''
    print(data)

    s_date = start_time.date()
    ss_time = start_time.time()
    b = str(ss_time).split(":")
    s_time = b[0]+":"+b[1]

    ee_time = end_time.time()
    b = str(ee_time).split(":")
    e_time = b[0]+":"+b[1]

    if user_role == 'patient':

        # push_data = PushNotifications.objects.filter(UserId=u_id,Title='Appointment reminder!', Body=f'Hi {u_name}, You have an appointment with {uu_name} in {days} days!',Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments')
        # if push_data is not None:

        a = device.send_message(Message(notification=Notification(title='Appointment reminder!',
                                                                  body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}'),
                                        webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                            link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'))))
        res = PushNotifications.objects.create(UserId=u_id, NotificationTime=current_time,
                                               Title='Appointment reminder!', Body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}', Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments')
        return a
    elif user_role in ['doctor', 'researcher', 'influencer']:
        # push_data = PushNotifications.objects.filter(UserId=u_id,Title='Appointment reminder!',Body=f'Hi {u_name}, You have an appointment with {uu_name} in {days} days!',Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment')
        # if push_data is not None:

        a = device.send_message(Message(notification=Notification(title='Appointment reminder!',
                                                                  body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}'),
                                        webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                            link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment'))))
        res = PushNotifications.objects.create(UserId=u_id, NotificationTime=current_time,
                                               Title='Appointment reminder!', Body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}', Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment')
        return a
    else:
        pass

    # print(f"in reminderrrrrrrrrrr response{a}")


def appointment_reminder(user_id, app_id):
    user_role = get_cu_user_type(user_id)
    current_time = timezone.now()
    n_e = notification_check("Appointment reminder")
    if user_role == 'patient' and n_e == True:
        app_data = Appointments.objects.filter(id=app_id)
        for x in app_data:
            slot_id = x.slot_id_id
            s_data = SchedulerSlots.objects.filter(id=x.slot_id_id)
            if s_data.exists():
                slot_data = SchedulerSlots.objects.filter(id=x.slot_id_id)[0]

                print(
                    f'-----------------------------slot_data----------{slot_data}')
                start_date = slot_data.schedule_start_time
                end_date = slot_data.schedule_end_time
                print(
                    f'--------------------------start_date-----------{start_date}')
                print(
                    f'--------------------------end_date-----------{end_date}')
                # start_date3 = datetime.strptime(str(parse_datetime(str(start_date))),format_data)
                # start_date2 = timezone.make_aware(parse_datetime(str(start_date)),timezone.get_current_timezone())
                converted_time1 = timezone.localtime(
                    start_date, timezone.get_current_timezone())
                print(
                    f'-------------------coverted_time---------{converted_time1}')
                converted_time2 = timezone.localtime(
                    end_date, timezone.get_current_timezone())
                print(
                    f'-------------------coverted_time---------{converted_time2}')

                doctor_id = slot_data.doctor_id
                uu_name = CuUser.objects.filter(id=doctor_id)[0].name
                a = SendPushReminder(
                    user_id, uu_name, "15", current_time, user_role, converted_time1, converted_time2)
    elif user_role in ['doctor', 'researcher', 'influencer'] and n_e == True:
        app_data = Appointments.objects.filter(id=app_id)
        slot_data = SchedulerSlots.objects.filter(id=app_data[0].slot_id.id)
        print(f"slot dataaaaaaaaaa{slot_data}")
        for x in slot_data:
            slot_id = x.id
            print(f"slot iddddddddddddd{slot_id}")
            p_data = Appointments.objects.filter(slot_id_id=slot_id)
            if p_data.exists():
                patient_data = Appointments.objects.filter(
                    slot_id_id=slot_id)[0]
                print(
                    f'-----------------------------slot_data----------{slot_data}')
                start_date = x.schedule_start_time
                end_date = x.schedule_end_time
                print(
                    f'--------------------------start_date-----------{start_date}')
                print(
                    f'--------------------------end_date-----------{end_date}')
                converted_time1 = timezone.localtime(parse_datetime(
                    str(start_date)), timezone.get_current_timezone())

                print(
                    f'-------------------converted_time1---------{converted_time1}')
                converted_time2 = timezone.localtime(
                    end_date, timezone.get_current_timezone())
                print(
                    f'-------------------converted_time---------{converted_time2}')

                patient_id = patient_data.patient_id
                uu_name = CuUser.objects.filter(id=patient_id)[0].name
                a = SendPushReminder(
                    user_id, uu_name, "15", current_time, user_role, converted_time1, converted_time2)


def notification_action(app_id, doctor_id, patient_id):
    n_s = notification_check('Appointment reminder')
    if n_s == True:
        try:
            a = appointment_reminder(doctor_id, app_id)
            a = appointment_reminder(patient_id, app_id)
            print(
                f'----------- appointment reminder response-------{a}------------')
        except Exception as e:
            print(e)

# added


def notification_jobs(app_id, start_time, doctor_id, patient_id):
    third_day_time = start_time + timedelta(minutes=15)
    scheduler.add_job(notification_action, 'date', run_date=third_day_time, args=[
                      app_id, doctor_id, patient_id])


# def CreateOrder(id,slot_id,summary,location,desc):
#     # payload="{}"
#     # headers = {
#     # "Content-Type": "application/json",
#     # "x-api-key": os.getenv("AIRWALLEX_API_KEY"),
#     # "x-client-id": os.getenv("AIRWALLEX_CLIENT_ID"),
#     # }

#     # url1=f"https://api-demo.airwallex.com/api/v1/authentication/login"

#     # res = requests.post(url1, json=payload, headers=headers)
#     # data = res.json()
#     # ACCESS_TOKEN = data["token"]

#     # AIRWALLEX_API_URL = "https://api-demo.airwallex.com/api/v1/pa/payment_intents/"
#     # url = f"{AIRWALLEX_API_URL}/{id}/"
#     # headers = {"Authorization": f"Bearer {ACCESS_TOKEN}",
#     #            "headers":"[object Object]",
#     #            "Content-Type": "application/json",}

#     # response = requests.get(url,json=payload, headers=headers)
#     # print(response.json())
#     # a = response.json()
#     # print(f"session details:--------------{a}----{type(a)}---{a is not None}")
#     try:
#             # user_is_valid = get_user_model().objects.filter(id__exact=a['metadata']['user_id']).exists()
#             # print(
#             #     f"user type appointment fixing--------{get_cu_user_type(a['metadata']['user_id'])}------------{user_is_valid}")
#             # if user_is_valid and get_cu_user_type(a['metadata']['user_id']) == "patient":
#             #     res = Appointments.objects.create(slot_id=SchedulerSlots.objects.get(id__exact=int(a['metadata']['slot_id'])),
#             #                                       patient=get_cu_user(int(a['metadata']['user_id'])), summary=a['metadata']['summary'],
#             #                                       location=a['metadata']['location'], description=a['metadata']['desc'])
#             #     a=AddCheckOutSessionDetails(a['id'],a['payment_intent'],a['metadata']['user_id'],res)

#             #     return res
#             user_is_valid = get_user_model().objects.filter(id__exact=id).exists()
#             print(
#                 f"user type appointment fixing--------{get_cu_user_type(id)}------------{user_is_valid}")
#             if user_is_valid and get_cu_user_type(id) == "patient":
#                 res = Appointments.objects.create(slot_id=SchedulerSlots.objects.get(id__exact=int(slot_id)),
#                                                   patient=get_cu_user(int(id)), summary=summary,
#                                                   location=location, description=desc)
#                 # a=AddCheckOutSessionDetails(a['id'],a['payment_intent'],a['metadata']['user_id'],res)

#                 return res
#             else:

#                 print(f"invalid user----------------")
#                 return False

#     except Exception as e:
#             print(f"orderrrrrrrrr exceptionnnnnnnnnnnnn---{e}")
#             return False
# #method for fulfilling order
# def FulfillOrder(AppId,user_id,slot_id):
#     # payload="{}"
#     # headers = {
#     # "Content-Type": "application/json",
#     # "x-api-key": os.getenv("AIRWALLEX_API_KEY"),
#     # "x-client-id": os.getenv("AIRWALLEX_CLIENT_ID"),
#     # }

#     # url1=f"https://api-demo.airwallex.com/api/v1/authentication/login"

#     # res = requests.post(url1, json=payload, headers=headers)
#     # data = res.json()
#     # ACCESS_TOKEN = data["token"]

#     # AIRWALLEX_API_URL = "https://api-demo.airwallex.com/api/v1/pa/payment_intents/"
#     # url = f"{AIRWALLEX_API_URL}/{id}/"
#     # headers = {"Authorization": f"Bearer {ACCESS_TOKEN}",
#     #            "headers":"[object Object]",
#     #            "Content-Type": "application/json",}

#     # response = requests.get(url,json=payload, headers=headers)
#     # print(response.json())
#     # a = response.json()
#     b=Appointments.objects.get(id__exact=AppId.id)
#     b.status = "B"
#     # b.Invoice=stripe.Invoice.retrieve(a['invoice']).invoice_pdf
#     b.save()
#     sess_details = add_session_details(AppId)
#     # #------------------------------------------------
#     # doctor_obj = SchedulerSlots.objects.get(id='slot_id').doctor.id
#     # patient_name = get_user_model().objects.get(id__exact=a['metadata']['user_id']).name
#     # doctor_name = SchedulerSlots.objects.get(id=a['metadata']['slot_id']).doctor.name
#     doctor_obj = SchedulerSlots.objects.get(id=slot_id).doctor.id
#     patient_name = get_user_model().objects.get(id__exact=user_id).name
#     doctor_name = SchedulerSlots.objects.get(id=slot_id).doctor.name
#     e_s=email_check('Appointment schedule')
#     if e_s==True:
#         try:
#             mail_status = send_meet_mail(AppId)
#         except Exception as e:
#             print(e)
#     notification_jobs(AppId.id, AppId.slot_id.schedule_start_time,doctor_obj,AppId.patient.id)
#     n_s=notification_check('Appointment schedule')
#     if n_s==True:
#         try:
#             a = SendPushNotification(doctor_obj, patient_name)
#             print(f'----------- appointment scheduled response-------{a}------------')
#         except Exception as e:
#             print(e)
#     a_n=SendAdminAppCre(AppId.id,doctor_name,patient_name)
#     print(f'----------- appointment created admin notification response-------{a_n}------------')


# NEW CREATE FULLFILL ORDER

def _map_status(airwallex_status):
    """Convert Airwallex status to your system's status"""
    status_map = {
        'requires_payment_method': 'REQUIRES_PAYMENT_METHOD',
        'requires_confirmation': 'REQUIRES_CONFIRMATION',
        'requires_action': 'REQUIRES_ACTION',
        'processing': 'PROCESSING',
        'succeeded': 'SUCCEEDED',
        'failed': 'FAILED',
        'canceled': 'CANCELED'
    }
    return status_map.get(airwallex_status.lower(), 'PROCESSING')


def CreateOrder(payment_intent_id, user_id=None, slot_id=None, summary=None, location=None, desc=None, currency=None, amount=None, raw_status=None):
    try:
        # Validate required fields
        if not all([user_id, slot_id]):
            print("Missing user_id or slot_id in metadata")
            return False

        # Get user and slot objects
        user = get_user_model().objects.filter(id=user_id).first()
        slot = SchedulerSlots.objects.filter(id=slot_id).first()

        if not user or not slot:
            print(f"Invalid user ({user_id}) or slot ({slot_id})")
            return False

        # Create appointment
        appointment = Appointments.objects.create(
            slot_id=slot,
            patient=user,
            summary=summary or "Medical Appointment",
            location=location or "Clinic",
            description=desc or "",
            status='P'  # Pending fulfillment
        )

        # Link payment to appointment
        PatientPayment.objects.create(
            AppointmentId=appointment,
            PaymentIntent=payment_intent_id,
            PatientId=user,
            currency=currency,  # Add currency field
            amount=amount,  # Add amount field to your model
            # payment_status='completed',
            payment_status=_map_status(raw_status),  # Convert to your status
            raw_status=raw_status,  # Store original Airwallex status
            payment_date=timezone.now()
        )

        print(
            f"Created appointment {appointment.id} for payment {payment_intent_id}")

        return appointment

    except Exception as e:
        print(f"CreateOrder failed: {str(e)}")
        return False


def FulfillOrder(appointment_id, user_id=None, slot_id=None):
    """
    Completes the appointment booking process:
    1. Marks slot as booked
    2. Sends notifications
    3. Updates appointment status
    """

    try:
        appointment = Appointments.objects.get(id=appointment_id.id)

        # If called from webhook, we might need to get these values
        if not user_id:
            user_id = appointment.patient.id
        if not slot_id:
            slot_id = appointment.slot_id.id
        # Mark slot as booked
        appointment.status = 'B'  # Booked
        appointment.save()

        # Get related objects
        slot = SchedulerSlots.objects.get(id=slot_id)
        doctor = slot.doctor
        patient = get_user_model().objects.get(id=user_id)

        # Add session details (assuming this creates meeting links, etc.)
        add_session_details(appointment_id)

        # Send email if enabled
        if email_check('Appointment schedule'):
            try:
                send_meet_mail(appointment_id)
            except Exception as e:
                print(f"Error sending email: {e}")

        # Send notifications
        notification_jobs(appointment_id.id,
                          slot.schedule_start_time, doctor.id, patient.id)

        if notification_check('Appointment schedule'):
            try:
                SendPushNotification(doctor.id, patient.name)
            except Exception as e:
                print(f"Error sending push notification: {e}")

        # Send admin notification
        SendAdminAppCre(appointment_id.id, doctor.name, patient.name)

        return True

    except Exception as e:
        print(f"Error fulfilling order: {e}")
        return False
# NEW CREATE FULLFILL ORDER

# --------------------------------------------------------------------------------
# update / delete scheduler slots only if status is null (i.e available)
class EditDeleteSlot(RetrieveUpdateDestroyAPIView):
    queryset = SchedulerSlots.objects.all()
    serializer_class = SlotSerializer
    lookup_field = 'id'

    def update(self, request, *args, **kwargs):
        instance = self.get_object()

        if instance.status not in [None, '', 'null']:
            return Response({"detail": "Only Available Slots can be updated."}, status=400)

        data = request.data.copy()

        data.pop('status', None)

        try:
            start_date = parse_datetime(data['start_date'])
            tz = timezone.get_current_timezone()

            data['schedule_start_time'] = timezone.make_aware(
                parse_datetime(f"{start_date.date()}T{data['schedule_start_time']}"), tz
            ).isoformat()
            data['schedule_end_time'] = timezone.make_aware(
                parse_datetime(f"{start_date.date()}T{data['schedule_end_time']}"), tz
            ).isoformat()

            user = get_user_model().objects.filter(email=data['doctor']).first()
            if not user:
                return Response({"doctor": ["User with this email does not exist."]}, status=400)
            data['doctor'] = user.id

        except Exception as e:
            return Response({"detail": f"Invalid input: {e}"}, status=400)

        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)
    

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        
        if instance.status not in [None, '', 'null']:
            return Response({"detail": "Only Available Slots can be Deleted."}, status=400)
        
        self.perform_destroy(instance)
        return Response({"detail": "Slot deleted successfully."}, status=200)

